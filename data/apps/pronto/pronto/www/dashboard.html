<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pronto Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.min.js"></script>
</head>
<body class="bg-gray-50">
    <div id="app" class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <div class="flex items-center">
                        <h1 class="text-2xl font-bold text-blue-600">Pronto</h1>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span id="user-name" class="text-gray-700"></span>
                        <button id="logout-btn" class="text-red-600 hover:text-red-800">
                            <i data-lucide="log-out" class="w-5 h-5"></i>
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <div class="px-4 py-6 sm:px-0">
                <!-- Welcome Section -->
                <div class="bg-white overflow-hidden shadow rounded-lg mb-6">
                    <div class="px-4 py-5 sm:p-6">
                        <h2 class="text-lg font-medium text-gray-900 mb-2">Welcome to Pronto!</h2>
                        <p class="text-gray-600">Your one-stop solution for print orders.</p>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div class="bg-white overflow-hidden shadow rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i data-lucide="plus-circle" class="w-8 h-8 text-blue-600"></i>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-medium text-gray-900">New Order</h3>
                                    <p class="text-gray-600">Create a new print order</p>
                                </div>
                            </div>
                            <div class="mt-4">
                                <button class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                    Create Order
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white overflow-hidden shadow rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i data-lucide="list" class="w-8 h-8 text-green-600"></i>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-medium text-gray-900">My Orders</h3>
                                    <p class="text-gray-600">View your order history</p>
                                </div>
                            </div>
                            <div class="mt-4">
                                <button class="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                                    View Orders
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white overflow-hidden shadow rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i data-lucide="credit-card" class="w-8 h-8 text-purple-600"></i>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-medium text-gray-900">Account</h3>
                                    <p class="text-gray-600">Manage your account</p>
                                </div>
                            </div>
                            <div class="mt-4">
                                <button class="w-full bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700">
                                    View Account
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Account Summary -->
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Account Summary</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600" id="credit-balance">₦0</div>
                                <div class="text-sm text-gray-600">Credit Balance</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600" id="loyalty-points">0</div>
                                <div class="text-sm text-gray-600">Loyalty Points</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-purple-600" id="total-orders">0</div>
                                <div class="text-sm text-gray-600">Total Orders</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Load user information
        async function loadUserInfo() {
            try {
                const response = await fetch('/api/method/pronto.api.auth.get_current_user_info');
                const data = await response.json();
                
                if (data.message && data.message.success) {
                    const user = data.message.user;
                    const customer = data.message.customer;
                    
                    document.getElementById('user-name').textContent = user.full_name || user.email;
                    
                    if (customer) {
                        document.getElementById('credit-balance').textContent = `₦${customer.credit_balance || 0}`;
                        document.getElementById('loyalty-points').textContent = customer.loyalty_points || 0;
                        document.getElementById('total-orders').textContent = customer.total_orders || 0;
                    }
                }
            } catch (error) {
                console.error('Error loading user info:', error);
            }
        }

        // Logout functionality
        document.getElementById('logout-btn').addEventListener('click', async () => {
            try {
                await fetch('/api/method/pronto.api.auth.logout', { method: 'POST' });
                window.location.href = '/login';
            } catch (error) {
                console.error('Logout error:', error);
            }
        });

        // Load data on page load
        document.addEventListener('DOMContentLoaded', () => {
            loadUserInfo();
        });
    </script>
</body>
</html>
