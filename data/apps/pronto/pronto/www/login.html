<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pronto Login</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://accounts.google.com/gsi/client" async defer></script>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-auto flex justify-center">
                <h1 class="text-4xl font-bold text-blue-600">Pronto</h1>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Sign in to your account
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Your one-stop solution for print orders
            </p>
        </div>
        
        <div class="mt-8 space-y-6">
            <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                <!-- Google Sign In Button -->
                <div class="flex justify-center">
                    <div id="g_id_onload"
                         data-client_id=""
                         data-callback="handleCredentialResponse"
                         data-auto_prompt="false">
                    </div>
                    <div class="g_id_signin"
                         data-type="standard"
                         data-size="large"
                         data-theme="outline"
                         data-text="sign_in_with"
                         data-shape="rectangular"
                         data-logo_alignment="left">
                    </div>
                </div>
                
                <!-- Manual Login Form (fallback) -->
                <div class="mt-6">
                    <div class="relative">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-300" />
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-2 bg-white text-gray-500">Or continue with email</span>
                        </div>
                    </div>
                    
                    <form class="mt-6 space-y-6" id="login-form">
                        <div>
                            <label for="email" class="sr-only">Email address</label>
                            <input id="email" name="email" type="email" autocomplete="email" required
                                   class="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                                   placeholder="Email address">
                        </div>
                        <div>
                            <label for="password" class="sr-only">Password</label>
                            <input id="password" name="password" type="password" autocomplete="current-password" required
                                   class="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                                   placeholder="Password">
                        </div>
                        
                        <div>
                            <button type="submit"
                                    class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Sign in
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Check Google OAuth configuration
        async function checkGoogleConfig() {
            try {
                const response = await fetch('/api/method/pronto.api.auth.check_google_oauth_config');
                const data = await response.json();
                
                if (data.message && data.message.configured && data.message.client_id) {
                    // Set the client ID for Google Sign-In
                    document.getElementById('g_id_onload').setAttribute('data-client_id', data.message.client_id);
                } else {
                    // Hide Google Sign-In if not configured
                    document.getElementById('g_id_onload').style.display = 'none';
                    document.querySelector('.g_id_signin').style.display = 'none';
                }
            } catch (error) {
                console.error('Error checking Google config:', error);
            }
        }

        // Handle Google OAuth response
        function handleCredentialResponse(response) {
            fetch('/api/method/pronto.api.auth.google_oauth_login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    credential: response.credential
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.message && data.message.success) {
                    window.location.href = data.message.redirect || '/dashboard';
                } else {
                    alert('Login failed: ' + (data.message.error || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Login error:', error);
                alert('Login failed. Please try again.');
            });
        }

        // Handle manual login form
        document.getElementById('login-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('/api/method/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        usr: email,
                        pwd: password
                    })
                });
                
                const data = await response.json();
                
                if (data.message === 'Logged In') {
                    window.location.href = '/dashboard';
                } else {
                    alert('Login failed. Please check your credentials.');
                }
            } catch (error) {
                console.error('Login error:', error);
                alert('Login failed. Please try again.');
            }
        });

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', () => {
            checkGoogleConfig();
        });
    </script>
</body>
</html>
