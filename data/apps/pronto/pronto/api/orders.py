import frappe
from frappe import _
import json
import random
import string
from datetime import datetime
import os

@frappe.whitelist(allow_guest=True)
def get_pricing_options():
    """
    Get all pricing options for the order form
    
    Returns:
        dict: All pricing options
    """
    try:
        # Get paper sizes
        paper_sizes = frappe.get_all("Print Paper Size", 
            filters={"is_active": 1},
            fields=["name", "size_name", "base_price"]
        )
        
        # Get paper types
        paper_types = frappe.get_all("Print Paper Type",
            filters={"is_active": 1}, 
            fields=["name", "type_name", "price_multiplier"]
        )
        
        # Get finishing options
        finishing_options = frappe.get_all("Print Finishing",
            filters={"is_active": 1},
            fields=["name", "finishing_name", "pricing_type", "additional_cost", "price_multiplier"]
        )
        
        # Get pickup locations
        pickup_locations = frappe.get_all("Pickup Location",
            filters={"is_active": 1},
            fields=["name", "location_name", "address"]
        )
        
        # Get priority options
        priority_options = frappe.get_all("Priority Pricing",
            filters={"is_active": 1},
            fields=["name", "priority_level", "turnaround_time", "additional_cost"]
        )
        
        # Get pricing configuration
        config = frappe.get_single("Pricing Configuration")
        
        return {
            "success": True,
            "data": {
                "paper_sizes": paper_sizes,
                "paper_types": paper_types,
                "finishing_options": finishing_options,
                "pickup_locations": pickup_locations,
                "priority_options": priority_options,
                "pricing_config": {
                    "color_black_white_multiplier": config.color_black_white_multiplier,
                    "color_color_multiplier": config.color_color_multiplier,
                    "sides_single_multiplier": config.sides_single_multiplier,
                    "sides_double_multiplier": config.sides_double_multiplier,
                    "tax_rate": config.tax_rate,
                    "service_charge_rate": config.service_charge_rate,
                    "minimum_order_amount": config.minimum_order_amount
                }
            }
        }
        
    except Exception as e:
        frappe.log_error(f"Get pricing options error: {str(e)}")
        return {"success": False, "error": str(e)}

@frappe.whitelist(allow_guest=True)
def calculate_print_price(
    paper_size, paper_type, color_option, sides,
    page_count, quantity, finishing=None, priority="Normal (2 hours)",
    coupon_code=None
):
    """
    Calculate print price based on specifications

    Returns:
        dict: Price breakdown
    """
    try:
        # Get pricing configuration
        config = frappe.get_single("Pricing Configuration")

        # Get base price from paper size
        paper_size_doc = frappe.get_doc("Print Paper Size", paper_size)
        base_price = paper_size_doc.base_price * int(page_count) * int(quantity)

        # Apply paper type multiplier
        paper_type_doc = frappe.get_doc("Print Paper Type", paper_type)
        paper_type_cost = base_price * paper_type_doc.price_multiplier

        # Apply color multiplier
        color_multiplier = config.color_color_multiplier if color_option == "Color" else config.color_black_white_multiplier
        color_cost = paper_type_cost * color_multiplier

        # Apply sides multiplier
        sides_multiplier = config.sides_double_multiplier if sides == "Double Sided" else config.sides_single_multiplier
        sides_cost = color_cost * sides_multiplier

        # Add finishing cost
        finishing_cost = 0
        if finishing:
            finishing_doc = frappe.get_doc("Print Finishing", finishing)
            if finishing_doc.pricing_type == "Fixed Cost":
                finishing_cost = finishing_doc.additional_cost
            else:
                finishing_cost = sides_cost * finishing_doc.price_multiplier

        # Add priority cost
        priority_cost = 0
        if priority:
            priority_doc = frappe.get_doc("Priority Pricing", priority)
            priority_cost = priority_doc.additional_cost

        # Calculate subtotal
        subtotal = sides_cost + finishing_cost + priority_cost

        # Apply discount
        discount_amount = 0
        discount_info = None
        if coupon_code:
            discount_result = validate_and_calculate_discount(coupon_code, subtotal)
            if discount_result.get("valid"):
                discount_amount = discount_result.get("discount_amount", 0)
                discount_info = discount_result

        # Calculate tax and service charge
        tax_amount = (subtotal - discount_amount) * (config.tax_rate / 100)
        service_charge = (subtotal - discount_amount) * (config.service_charge_rate / 100)

        # Calculate total
        total = subtotal - discount_amount + tax_amount + service_charge

        return {
            "success": True,
            "breakdown": {
                "base_price": base_price,
                "paper_type_cost": paper_type_cost,
                "color_cost": color_cost,
                "sides_cost": sides_cost,
                "finishing_cost": finishing_cost,
                "priority_cost": priority_cost,
                "subtotal": subtotal,
                "discount_amount": discount_amount,
                "tax_amount": tax_amount,
                "service_charge": service_charge,
                "total": total
            },
            "discount_info": discount_info
        }

    except Exception as e:
        frappe.log_error(f"Price calculation error: {str(e)}")
        return {"success": False, "error": str(e)}

@frappe.whitelist()
def get_customer_orders(limit=10, offset=0):
    """
    Get orders for the current customer
    
    Args:
        limit (int): Number of orders to return
        offset (int): Offset for pagination
        
    Returns:
        dict: Customer orders
    """
    try:
        # Get customer
        customer = frappe.get_value("Pronto Customer", {"user": frappe.session.user}, "name")
        if not customer:
            return {"success": False, "error": "Customer record not found"}
        
        # Get orders
        orders = frappe.get_all("Print Order",
            filters={"customer": customer},
            fields=[
                "name", "order_date", "status", "priority", "total_amount",
                "pickup_location", "pickup_code", "payment_status"
            ],
            order_by="order_date desc",
            limit=limit,
            start=offset
        )
        
        # Get total count
        total_count = frappe.db.count("Print Order", {"customer": customer})
        
        return {
            "success": True,
            "data": {
                "orders": orders,
                "total_count": total_count,
                "has_more": (offset + limit) < total_count
            }
        }
        
    except Exception as e:
        frappe.log_error(f"Get customer orders error: {str(e)}")
        return {"success": False, "error": str(e)}

def validate_and_calculate_discount(coupon_code, order_amount):
    """Validate coupon and calculate discount"""
    try:
        coupon = frappe.get_doc("Discount Coupon", coupon_code.upper())

        # Check if active
        if not coupon.is_active:
            return {"valid": False, "error": "Coupon is not active"}

        # Check date validity
        from datetime import date
        today = date.today()
        if today < coupon.valid_from or today > coupon.valid_to:
            return {"valid": False, "error": "Coupon has expired or not yet valid"}

        # Check usage limit
        if coupon.usage_limit > 0 and coupon.usage_count >= coupon.usage_limit:
            return {"valid": False, "error": "Coupon usage limit reached"}

        # Check minimum order amount
        if order_amount < coupon.minimum_order_amount:
            return {"valid": False, "error": f"Minimum order amount is ₦{coupon.minimum_order_amount}"}

        # Calculate discount
        if coupon.discount_type == "Percentage":
            discount = order_amount * (coupon.discount_value / 100)
            if coupon.maximum_discount_amount > 0:
                discount = min(discount, coupon.maximum_discount_amount)
        else:  # Fixed Amount
            discount = coupon.discount_value

        return {
            "valid": True,
            "discount_amount": discount,
            "coupon_name": coupon.coupon_name,
            "discount_type": coupon.discount_type,
            "discount_value": coupon.discount_value
        }

    except frappe.DoesNotExistError:
        return {"valid": False, "error": "Invalid coupon code"}
    except Exception as e:
        frappe.log_error(f"Coupon validation error: {str(e)}")
        return {"valid": False, "error": "Error validating coupon"}
