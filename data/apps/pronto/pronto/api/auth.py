import frappe
from frappe import _
import jwt
import requests
from datetime import datetime

@frappe.whitelist(allow_guest=True)
def google_oauth_login(credential):
    """
    Handle Google OAuth login with JWT credential

    Args:
        credential (str): JWT token from Google

    Returns:
        dict: Login status and user info
    """
    try:
        # Decode JWT token
        decoded = jwt.decode(credential, options={"verify_signature": False})

        email = decoded.get('email')
        name = decoded.get('name')
        picture = decoded.get('picture')

        if not email:
            return {"success": False, "error": "Email not found in token"}

        # Check if user exists
        if not frappe.db.exists("User", email):
            # Create new user
            user = frappe.get_doc({
                "doctype": "User",
                "email": email,
                "first_name": name.split()[0] if name else email.split('@')[0],
                "last_name": name.split()[-1] if name and len(name.split()) > 1 else "",
                "user_image": picture,
                "enabled": 1,
                "send_welcome_email": 0
            })
            user.insert(ignore_permissions=True)

            # Add Customer role
            user.add_roles("Customer")

            # Create Pronto Customer
            create_pronto_customer(email, name)

        # Login user
        frappe.local.login_manager.login_as(email)
        frappe.db.commit()

        return {
            "success": True,
            "message": "Login successful",
            "user": email,
            "redirect": "/dashboard"
        }

    except Exception as e:
        frappe.log_error(f"Google OAuth Error: {str(e)}")
        return {"success": False, "error": str(e)}

def create_pronto_customer(email, name):
    """Create Pronto Customer record"""
    if not frappe.db.exists("Pronto Customer", name):
        customer = frappe.get_doc({
            "doctype": "Pronto Customer",
            "customer_name": name or email.split('@')[0],
            "email": email,
            "user": email,
            "customer_status": "Active",
            "credit_balance": 0,
            "loyalty_points": 0
        })
        customer.insert(ignore_permissions=True)

@frappe.whitelist(allow_guest=True)
def check_google_oauth_config():
    """Check if Google OAuth is configured"""
    # Check for Google OAuth settings in site config
    client_id = frappe.conf.get('google_client_id')
    return {"configured": bool(client_id), "client_id": client_id}

@frappe.whitelist()
def get_current_user_info():
    """Get current user information"""
    try:
        user = frappe.get_doc("User", frappe.session.user)
        customer = None
        
        # Try to get customer record
        if frappe.db.exists("Pronto Customer", {"user": frappe.session.user}):
            customer = frappe.get_doc("Pronto Customer", {"user": frappe.session.user})
        
        return {
            "success": True,
            "user": {
                "email": user.email,
                "full_name": user.full_name,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "user_image": user.user_image,
                "roles": [role.role for role in user.roles]
            },
            "customer": {
                "name": customer.name if customer else None,
                "customer_name": customer.customer_name if customer else None,
                "credit_balance": customer.credit_balance if customer else 0,
                "loyalty_points": customer.loyalty_points if customer else 0
            } if customer else None
        }
    except Exception as e:
        frappe.log_error(f"Get user info error: {str(e)}")
        return {"success": False, "error": str(e)}

@frappe.whitelist(allow_guest=True)
def logout():
    """Logout current user"""
    try:
        frappe.local.login_manager.logout()
        return {"success": True, "message": "Logged out successfully"}
    except Exception as e:
        return {"success": False, "error": str(e)}
