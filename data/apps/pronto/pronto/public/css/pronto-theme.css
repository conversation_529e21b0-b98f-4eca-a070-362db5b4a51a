/* Pronto Theme CSS */

:root {
  --pronto-primary: #2563eb;
  --pronto-primary-dark: #1d4ed8;
  --pronto-secondary: #10b981;
  --pronto-accent: #8b5cf6;
  --pronto-danger: #ef4444;
  --pronto-warning: #f59e0b;
  --pronto-success: #10b981;
  --pronto-info: #3b82f6;
  --pronto-light: #f8fafc;
  --pronto-dark: #1e293b;
}

/* Base styles */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Button styles */
.btn-pronto {
  background-color: var(--pronto-primary);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-pronto:hover {
  background-color: var(--pronto-primary-dark);
}

.btn-pronto-secondary {
  background-color: var(--pronto-secondary);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-pronto-secondary:hover {
  background-color: #059669;
}

/* Card styles */
.pronto-card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
  margin-bottom: 1rem;
}

/* Status badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-draft { background-color: #f3f4f6; color: #374151; }
.status-submitted { background-color: #dbeafe; color: #1e40af; }
.status-paid { background-color: #d1fae5; color: #065f46; }
.status-in-progress { background-color: #fef3c7; color: #92400e; }
.status-completed { background-color: #d1fae5; color: #065f46; }
.status-cancelled { background-color: #fee2e2; color: #991b1b; }

/* Form styles */
.pronto-form-group {
  margin-bottom: 1rem;
}

.pronto-form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
}

.pronto-form-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.pronto-form-input:focus {
  outline: none;
  border-color: var(--pronto-primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Loading spinner */
.pronto-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid #f3f4f6;
  border-radius: 50%;
  border-top-color: var(--pronto-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Notification styles */
.pronto-notification {
  position: fixed;
  top: 1rem;
  right: 1rem;
  max-width: 24rem;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  padding: 1rem;
  z-index: 50;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
}

.pronto-notification.show {
  transform: translateX(0);
}

.pronto-notification.success {
  border-left: 4px solid var(--pronto-success);
}

.pronto-notification.error {
  border-left: 4px solid var(--pronto-danger);
}

.pronto-notification.warning {
  border-left: 4px solid var(--pronto-warning);
}

.pronto-notification.info {
  border-left: 4px solid var(--pronto-info);
}

/* Responsive utilities */
@media (max-width: 640px) {
  .pronto-card {
    padding: 1rem;
    margin: 0.5rem;
  }
  
  .pronto-notification {
    left: 1rem;
    right: 1rem;
    max-width: none;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .pronto-card {
    box-shadow: none;
    border: 1px solid #e5e7eb;
  }
}
