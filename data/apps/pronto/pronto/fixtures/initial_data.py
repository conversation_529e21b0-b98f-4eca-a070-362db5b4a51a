import frappe

def main():
    """Initialize master data after app installation"""
    create_roles()
    create_paper_sizes()
    create_paper_types()
    create_finishing_options()
    create_priority_pricing()
    create_pricing_configuration()
    create_pickup_locations()
    create_loyalty_configuration()
    frappe.db.commit()

def create_roles():
    """Create custom roles"""
    roles = [
        "Customer",
        "Print Staff",
        "QA Staff",
        "Dispatch Staff",
        "Collection Staff",
        "Customer Service",
        "Business Manager",
        "Print Shop Manager"
    ]
    
    for role_name in roles:
        if not frappe.db.exists("Role", role_name):
            role = frappe.get_doc({
                "doctype": "Role",
                "role_name": role_name,
                "desk_access": 1 if role_name != "Customer" else 0
            })
            role.insert(ignore_permissions=True)

def create_paper_sizes():
    """Create default paper sizes"""
    sizes = [
        {"size_name": "A4", "base_price": 50},
        {"size_name": "A3", "base_price": 100},
        {"size_name": "A5", "base_price": 30},
        {"size_name": "Letter", "base_price": 50},
        {"size_name": "Legal", "base_price": 60},
    ]
    
    for size in sizes:
        if not frappe.db.exists("Print Paper Size", size["size_name"]):
            doc = frappe.get_doc({
                "doctype": "Print Paper Size",
                "size_name": size["size_name"],
                "base_price": size["base_price"],
                "is_active": 1
            })
            doc.insert(ignore_permissions=True)

def create_paper_types():
    """Create default paper types"""
    types = [
        {"type_name": "Plain", "price_multiplier": 1.0},
        {"type_name": "Premium", "price_multiplier": 1.5},
        {"type_name": "Glossy", "price_multiplier": 1.8},
    ]
    
    for ptype in types:
        if not frappe.db.exists("Print Paper Type", ptype["type_name"]):
            doc = frappe.get_doc({
                "doctype": "Print Paper Type",
                "type_name": ptype["type_name"],
                "price_multiplier": ptype["price_multiplier"],
                "is_active": 1
            })
            doc.insert(ignore_permissions=True)

def create_finishing_options():
    """Create default finishing options"""
    options = [
        {"finishing_name": "Binding", "pricing_type": "Fixed Cost", "additional_cost": 100},
        {"finishing_name": "Lamination", "pricing_type": "Fixed Cost", "additional_cost": 150},
        {"finishing_name": "Stapling", "pricing_type": "Fixed Cost", "additional_cost": 20},
    ]
    
    for option in options:
        if not frappe.db.exists("Print Finishing", option["finishing_name"]):
            doc = frappe.get_doc({
                "doctype": "Print Finishing",
                **option,
                "is_active": 1
            })
            doc.insert(ignore_permissions=True)

def create_priority_pricing():
    """Create priority pricing levels"""
    priorities = [
        {"priority_level": "No Wahala", "turnaround_time": "Next Day", "additional_cost": 0},
        {"priority_level": "Normal (2 hours)", "turnaround_time": "2 Hours", "additional_cost": 0},
        {"priority_level": "Sharp Sharp", "turnaround_time": "1 Hour", "additional_cost": 200},
        {"priority_level": "Pronto", "turnaround_time": "30 Minutes", "additional_cost": 500},
    ]
    
    for priority in priorities:
        if not frappe.db.exists("Priority Pricing", priority["priority_level"]):
            doc = frappe.get_doc({
                "doctype": "Priority Pricing",
                **priority,
                "is_active": 1
            })
            doc.insert(ignore_permissions=True)

def create_pricing_configuration():
    """Create default pricing configuration"""
    if not frappe.db.exists("Pricing Configuration"):
        doc = frappe.get_doc({
            "doctype": "Pricing Configuration",
            "color_black_white_multiplier": 1.0,
            "color_color_multiplier": 1.5,
            "sides_single_multiplier": 1.0,
            "sides_double_multiplier": 1.8,
            "default_currency": "NGN",
            "minimum_order_amount": 0,
            "tax_rate": 0,
            "service_charge_rate": 0,
            "is_active": 1
        })
        doc.insert(ignore_permissions=True)

def create_pickup_locations():
    """Create default pickup locations"""
    locations = [
        {"location_name": "H-Medix Wuse 2", "address": "Wuse 2, Abuja"},
        {"location_name": "Garki Shopping Mall", "address": "Garki, Abuja"},
        {"location_name": "Wuse Market", "address": "Wuse Market, Abuja"},
    ]
    
    for location in locations:
        if not frappe.db.exists("Pickup Location", location["location_name"]):
            doc = frappe.get_doc({
                "doctype": "Pickup Location",
                **location,
                "is_active": 1
            })
            doc.insert(ignore_permissions=True)

def create_loyalty_configuration():
    """Create loyalty points configuration"""
    if not frappe.db.exists("Loyalty Configuration"):
        doc = frappe.get_doc({
            "doctype": "Loyalty Configuration",
            "points_per_currency_unit": 1,  # 1 point per ₦100
            "currency_unit_value": 100,
            "points_to_credit_ratio": 0.5,  # 100 points = ₦50
            "minimum_points_for_conversion": 100,
            "points_expiry_days": 0,  # No expiry
            "is_active": 1
        })
        doc.insert(ignore_permissions=True)
