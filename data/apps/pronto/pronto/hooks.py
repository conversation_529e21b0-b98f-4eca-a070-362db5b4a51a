from . import __version__ as app_version

app_name = "pronto"
app_title = "Pronto"
app_publisher = "Pronto Team"
app_description = "Print Order Management System"
app_email = "<EMAIL>"
app_license = "MIT"

# Includes in <head>
app_include_css = [
    "/assets/pronto/css/pronto-theme.css"
]

app_include_js = [
    "https://unpkg.com/lucide@latest/dist/umd/lucide.min.js"
]

# Web pages
web_include_css = [
    "/assets/pronto/css/pronto-theme.css"
]

web_include_js = [
    "https://unpkg.com/lucide@latest/dist/umd/lucide.min.js"
]

# Home Pages
# ----------
website_route_rules = [
    {"from_route": "/dashboard", "to_route": "dashboard"},
    {"from_route": "/print-staff", "to_route": "print-staff"},
    {"from_route": "/qa-staff", "to_route": "qa-staff"},
    {"from_route": "/dispatch-staff", "to_route": "dispatch-staff"},
    {"from_route": "/collection-staff", "to_route": "collection-staff"},
    {"from_route": "/business-manager", "to_route": "business-manager"},
]

# Role-based home pages
role_home_page = {
    "Customer": "/dashboard",
    "Print Staff": "/print-staff",
    "QA Staff": "/qa-staff",
    "Dispatch Staff": "/dispatch-staff",
    "Collection Staff": "/collection-staff",
    "Business Manager": "/business-manager",
}

# Scheduled Tasks
# ---------------
scheduler_events = {
    "daily": [
        "pronto.tasks.cleanup_old_files"
    ],
    "hourly": [
        "pronto.tasks.check_payment_status"
    ]
}

# DocType Events
# --------------
doc_events = {
    "Print Order": {
        "after_insert": "pronto.pronto.doctype.print_order.print_order.create_job_queue_entry",
        "on_update": "pronto.pronto.doctype.print_order.print_order.update_customer_stats",
        "on_payment_success": "pronto.pronto.doctype.print_order.print_order.award_loyalty_points"
    },
    "Job Queue": {
        "on_update": "pronto.api.staff.notify_job_update"
    }
}

# After Install
# -------------
after_install = "pronto.fixtures.initial_data.main"

# Permissions
# -----------
permission_query_conditions = {
    "Print Order": "pronto.pronto.doctype.print_order.print_order.get_permission_query_conditions",
}

has_permission = {
    "Print Order": "pronto.pronto.doctype.print_order.print_order.has_permission",
}

# Website Settings
# ----------------
website_context = {
    "favicon": "/assets/pronto/images/favicon.png",
    "splash_image": "/assets/pronto/images/splash.png"
}

# Fixtures
# --------
fixtures = [
    {
        "dt": "Custom Field",
        "filters": [
            ["name", "in", [
                "User-assigned_print_center"
            ]]
        ]
    }
]
