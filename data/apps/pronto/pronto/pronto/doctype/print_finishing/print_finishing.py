import frappe
from frappe.model.document import Document

class PrintFinishing(Document):
    def validate(self):
        """Validate print finishing data"""
        if self.pricing_type == "Fixed Cost" and not self.additional_cost:
            frappe.throw("Additional cost is required for fixed cost pricing")
        
        if self.pricing_type == "Multiplier" and not self.price_multiplier:
            frappe.throw("Price multiplier is required for multiplier pricing")
        
        if self.pricing_type == "Multiplier" and self.price_multiplier <= 0:
            frappe.throw("Price multiplier must be greater than 0")
