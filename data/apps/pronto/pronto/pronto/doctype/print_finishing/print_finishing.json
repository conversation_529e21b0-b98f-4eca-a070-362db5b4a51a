{"actions": [], "allow_rename": 0, "autoname": "field:finishing_name", "creation": "2024-01-01 00:00:00", "doctype": "DocType", "engine": "InnoDB", "field_order": ["finishing_name", "pricing_type", "additional_cost", "price_multiplier", "description", "is_active"], "fields": [{"fieldname": "finishing_name", "fieldtype": "Data", "label": "Finishing Name", "reqd": 1, "unique": 1}, {"fieldname": "pricing_type", "fieldtype": "Select", "label": "Pricing Type", "options": "Fixed Cost\nMultiplier", "default": "Fixed Cost", "reqd": 1}, {"fieldname": "additional_cost", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Additional Cost", "depends_on": "eval:doc.pricing_type=='Fixed Cost'"}, {"fieldname": "price_multiplier", "fieldtype": "Float", "label": "Price Multiplier", "depends_on": "eval:doc.pricing_type=='Multiplier'", "default": 1.0}, {"fieldname": "description", "fieldtype": "Text", "label": "Description"}, {"fieldname": "is_active", "fieldtype": "Check", "label": "Is Active", "default": 1}], "modified": "2024-01-01 00:00:00", "module": "Pronto", "name": "Print Finishing", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"read": 1, "role": "Customer"}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}