{"actions": [], "allow_rename": 0, "autoname": "format:PRICING-CONFIG", "creation": "2024-01-01 00:00:00", "doctype": "DocType", "engine": "InnoDB", "is_single": 1, "field_order": ["color_pricing_section", "color_black_white_multiplier", "color_color_multiplier", "sides_pricing_section", "sides_single_multiplier", "sides_double_multiplier", "general_settings_section", "default_currency", "minimum_order_amount", "column_break_1", "tax_rate", "service_charge_rate", "status_section", "is_active"], "fields": [{"fieldname": "color_pricing_section", "fieldtype": "Section Break", "label": "Color Pricing"}, {"fieldname": "color_black_white_multiplier", "fieldtype": "Float", "label": "Black & White Multiplier", "default": 1.0, "reqd": 1}, {"fieldname": "color_color_multiplier", "fieldtype": "Float", "label": "Color Multiplier", "default": 1.5, "reqd": 1}, {"fieldname": "sides_pricing_section", "fieldtype": "Section Break", "label": "Sides Pricing"}, {"fieldname": "sides_single_multiplier", "fieldtype": "Float", "label": "Single Sided Multiplier", "default": 1.0, "reqd": 1}, {"fieldname": "sides_double_multiplier", "fieldtype": "Float", "label": "Double Sided Multiplier", "default": 1.8, "reqd": 1}, {"fieldname": "general_settings_section", "fieldtype": "Section Break", "label": "General Settings"}, {"fieldname": "default_currency", "fieldtype": "Data", "label": "<PERSON><PERSON><PERSON>", "default": "NGN", "reqd": 1}, {"fieldname": "minimum_order_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Minimum Order Amount", "default": 0}, {"fieldname": "column_break_1", "fieldtype": "Column Break"}, {"fieldname": "tax_rate", "fieldtype": "Float", "label": "Tax Rate (%)", "default": 0}, {"fieldname": "service_charge_rate", "fieldtype": "Float", "label": "Service Charge Rate (%)", "default": 0}, {"fieldname": "status_section", "fieldtype": "Section Break", "label": "Status"}, {"fieldname": "is_active", "fieldtype": "Check", "label": "Is Active", "default": 1}], "modified": "2024-01-01 00:00:00", "module": "Pronto", "name": "Pricing Configuration", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"read": 1, "role": "Business Manager", "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}