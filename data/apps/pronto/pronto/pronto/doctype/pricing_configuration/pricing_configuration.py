import frappe
from frappe.model.document import Document

class PricingConfiguration(Document):
    def validate(self):
        """Validate pricing configuration"""
        if self.color_black_white_multiplier <= 0:
            frappe.throw("Black & White multiplier must be greater than 0")
        
        if self.color_color_multiplier <= 0:
            frappe.throw("Color multiplier must be greater than 0")
        
        if self.sides_single_multiplier <= 0:
            frappe.throw("Single sided multiplier must be greater than 0")
        
        if self.sides_double_multiplier <= 0:
            frappe.throw("Double sided multiplier must be greater than 0")
        
        if self.tax_rate < 0 or self.tax_rate > 100:
            frappe.throw("Tax rate must be between 0 and 100")
        
        if self.service_charge_rate < 0 or self.service_charge_rate > 100:
            frappe.throw("Service charge rate must be between 0 and 100")
