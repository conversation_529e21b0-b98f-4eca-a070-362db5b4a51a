import frappe
from frappe.model.document import Document

class PrintPaperSize(Document):
    def validate(self):
        """Validate paper size data"""
        if self.base_price < 0:
            frappe.throw("Base price cannot be negative")
    
    def before_save(self):
        """Before save operations"""
        # Ensure size name is uppercase
        if self.size_name:
            self.size_name = self.size_name.upper()
