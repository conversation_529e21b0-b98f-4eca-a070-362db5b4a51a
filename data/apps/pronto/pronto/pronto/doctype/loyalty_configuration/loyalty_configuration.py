import frappe
from frappe.model.document import Document

class LoyaltyConfiguration(Document):
    def validate(self):
        """Validate loyalty configuration"""
        if self.points_per_currency_unit <= 0:
            frappe.throw("Points per currency unit must be greater than 0")
        
        if self.currency_unit_value <= 0:
            frappe.throw("Currency unit value must be greater than 0")
        
        if self.points_to_credit_ratio <= 0:
            frappe.throw("Points to credit ratio must be greater than 0")
        
        if self.minimum_points_for_conversion < 0:
            frappe.throw("Minimum points for conversion cannot be negative")
        
        if self.points_expiry_days < 0:
            frappe.throw("Points expiry days cannot be negative")
