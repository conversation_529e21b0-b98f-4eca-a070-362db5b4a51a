import frappe
from frappe.model.document import Document

class CustomerCreditTransaction(Document):
    def validate(self):
        """Validate credit transaction"""
        if not self.customer:
            frappe.throw("Customer is required")
        
        if not self.transaction_type:
            frappe.throw("Transaction type is required")
    
    def before_insert(self):
        """Set created by user"""
        if not self.created_by:
            self.created_by = frappe.session.user
