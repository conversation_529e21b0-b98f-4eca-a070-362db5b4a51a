{"actions": [], "allow_rename": 0, "autoname": "format:TXN-{YYYY}-{#####}", "creation": "2024-01-01 00:00:00", "doctype": "DocType", "engine": "InnoDB", "field_order": ["customer", "transaction_type", "amount", "points", "column_break_1", "balance_before", "balance_after", "related_order", "payment_reference", "notes", "audit_section", "transaction_date", "created_by"], "fields": [{"fieldname": "customer", "fieldtype": "Link", "label": "Customer", "options": "Pronto Customer", "reqd": 1}, {"fieldname": "transaction_type", "fieldtype": "Select", "label": "Transaction Type", "options": "Credit Added\nCredit Used\nPoints Converted\nRefund", "reqd": 1}, {"fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount", "reqd": 1}, {"fieldname": "points", "fieldtype": "Int", "label": "Points", "default": 0}, {"fieldname": "column_break_1", "fieldtype": "Column Break"}, {"fieldname": "balance_before", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Balance Before", "read_only": 1}, {"fieldname": "balance_after", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Balance After", "read_only": 1}, {"fieldname": "related_order", "fieldtype": "Link", "label": "Related Order", "options": "Print Order"}, {"fieldname": "payment_reference", "fieldtype": "Data", "label": "Payment Reference"}, {"fieldname": "notes", "fieldtype": "Text", "label": "Notes"}, {"fieldname": "audit_section", "fieldtype": "Section Break", "label": "Audit"}, {"fieldname": "transaction_date", "fieldtype": "Datetime", "label": "Transaction Date", "default": "now"}, {"fieldname": "created_by", "fieldtype": "Link", "label": "Created By", "options": "User", "default": "user"}], "modified": "2024-01-01 00:00:00", "module": "Pronto", "name": "Customer Credit Transaction", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"read": 1, "role": "Customer", "if_owner": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}