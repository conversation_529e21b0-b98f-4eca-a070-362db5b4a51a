{"actions": [], "allow_rename": 0, "autoname": "field:priority_level", "creation": "2024-01-01 00:00:00", "doctype": "DocType", "engine": "InnoDB", "field_order": ["priority_level", "turnaround_time", "additional_cost", "description", "is_active"], "fields": [{"fieldname": "priority_level", "fieldtype": "Data", "label": "Priority Level", "reqd": 1, "unique": 1}, {"fieldname": "turnaround_time", "fieldtype": "Data", "label": "Turnaround Time", "reqd": 1}, {"fieldname": "additional_cost", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Additional Cost", "default": 0}, {"fieldname": "description", "fieldtype": "Text", "label": "Description"}, {"fieldname": "is_active", "fieldtype": "Check", "label": "Is Active", "default": 1}], "modified": "2024-01-01 00:00:00", "module": "Pronto", "name": "Priority Pricing", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"read": 1, "role": "Customer"}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}