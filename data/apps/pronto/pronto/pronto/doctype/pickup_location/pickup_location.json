{"actions": [], "allow_rename": 0, "autoname": "field:location_name", "creation": "2024-01-01 00:00:00", "doctype": "DocType", "engine": "InnoDB", "field_order": ["location_name", "address", "phone_number", "operating_hours", "is_active"], "fields": [{"fieldname": "location_name", "fieldtype": "Data", "label": "Location Name", "reqd": 1, "unique": 1}, {"fieldname": "address", "fieldtype": "Text", "label": "Address", "reqd": 1}, {"fieldname": "phone_number", "fieldtype": "Data", "label": "Phone Number"}, {"fieldname": "operating_hours", "fieldtype": "Text", "label": "Operating Hours"}, {"fieldname": "is_active", "fieldtype": "Check", "label": "Is Active", "default": 1}], "modified": "2024-01-01 00:00:00", "module": "Pronto", "name": "Pickup Location", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"read": 1, "role": "Customer"}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}