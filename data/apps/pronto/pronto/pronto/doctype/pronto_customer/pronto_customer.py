import frappe
from frappe.model.document import Document

class ProntoCustomer(Document):
    def validate(self):
        """Validate customer data"""
        if self.credit_balance < 0:
            frappe.throw("Credit balance cannot be negative")
        
        if self.loyalty_points < 0:
            frappe.throw("Loyalty points cannot be negative")
    
    def update_stats(self):
        """Update customer statistics"""
        # Get order statistics
        orders = frappe.db.sql("""
            SELECT 
                COUNT(*) as total_orders,
                SUM(total_amount) as total_spent,
                MAX(order_date) as last_order_date
            FROM `tabPrint Order`
            WHERE customer = %s AND status != 'Cancelled'
        """, (self.name,), as_dict=True)
        
        if orders and orders[0]:
            self.total_orders = orders[0].total_orders or 0
            self.total_spent = orders[0].total_spent or 0
            self.last_order_date = orders[0].last_order_date
            self.save(ignore_permissions=True)
    
    def add_credit(self, amount, transaction_type="Credit Added", notes=None, related_order=None):
        """Add credit to customer account"""
        if amount <= 0:
            frappe.throw("Credit amount must be positive")
        
        balance_before = self.credit_balance
        self.credit_balance += amount
        balance_after = self.credit_balance
        
        # Create transaction log
        transaction = frappe.get_doc({
            "doctype": "Customer Credit Transaction",
            "customer": self.name,
            "transaction_type": transaction_type,
            "amount": amount,
            "balance_before": balance_before,
            "balance_after": balance_after,
            "related_order": related_order,
            "notes": notes
        })
        transaction.insert(ignore_permissions=True)
        
        self.save(ignore_permissions=True)
        return transaction
    
    def use_credit(self, amount, related_order=None, notes=None):
        """Use credit from customer account"""
        if amount <= 0:
            frappe.throw("Credit amount must be positive")
        
        if self.credit_balance < amount:
            frappe.throw("Insufficient credit balance")
        
        balance_before = self.credit_balance
        self.credit_balance -= amount
        balance_after = self.credit_balance
        
        # Create transaction log
        transaction = frappe.get_doc({
            "doctype": "Customer Credit Transaction",
            "customer": self.name,
            "transaction_type": "Credit Used",
            "amount": -amount,
            "balance_before": balance_before,
            "balance_after": balance_after,
            "related_order": related_order,
            "notes": notes
        })
        transaction.insert(ignore_permissions=True)
        
        self.save(ignore_permissions=True)
        return transaction
    
    def add_loyalty_points(self, points, related_order=None):
        """Add loyalty points to customer account"""
        if points > 0:
            self.loyalty_points += points
            self.save(ignore_permissions=True)
    
    def convert_points_to_credit(self, points):
        """Convert loyalty points to credit"""
        if points <= 0:
            frappe.throw("Points must be positive")
        
        if self.loyalty_points < points:
            frappe.throw("Insufficient loyalty points")
        
        # Get loyalty configuration
        config = frappe.get_single("Loyalty Configuration")
        if not config.is_active:
            frappe.throw("Loyalty program is not active")
        
        if points < config.minimum_points_for_conversion:
            frappe.throw(f"Minimum {config.minimum_points_for_conversion} points required for conversion")
        
        # Calculate credit amount
        credit_amount = points * config.points_to_credit_ratio
        
        # Deduct points
        self.loyalty_points -= points
        
        # Add credit
        self.add_credit(
            credit_amount, 
            transaction_type="Points Converted",
            notes=f"Converted {points} points to credit"
        )
        
        return credit_amount
