{"actions": [], "allow_rename": 0, "autoname": "field:customer_name", "creation": "2024-01-01 00:00:00", "doctype": "DocType", "engine": "InnoDB", "field_order": ["customer_information_section", "customer_name", "email", "phone_number", "column_break_1", "user", "customer_status", "registration_date", "financial_section", "credit_balance", "loyalty_points", "column_break_2", "total_orders", "total_spent", "last_order_date", "preferences_section", "preferred_paper_size", "preferred_paper_type", "column_break_3", "preferred_pickup_location", "notification_preferences"], "fields": [{"fieldname": "customer_information_section", "fieldtype": "Section Break", "label": "Customer Information"}, {"fieldname": "customer_name", "fieldtype": "Data", "label": "Customer Name", "reqd": 1, "unique": 1}, {"fieldname": "email", "fieldtype": "Data", "label": "Email", "options": "Email", "reqd": 1, "unique": 1}, {"fieldname": "phone_number", "fieldtype": "Data", "label": "Phone Number"}, {"fieldname": "column_break_1", "fieldtype": "Column Break"}, {"fieldname": "user", "fieldtype": "Link", "label": "User", "options": "User"}, {"fieldname": "customer_status", "fieldtype": "Select", "label": "Status", "options": "Active\nInactive\nSuspended", "default": "Active"}, {"fieldname": "registration_date", "fieldtype": "Date", "label": "Registration Date", "default": "Today"}, {"fieldname": "financial_section", "fieldtype": "Section Break", "label": "Financial Information"}, {"fieldname": "credit_balance", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Credit Balance", "default": 0}, {"fieldname": "loyalty_points", "fieldtype": "Int", "label": "Loyalty Points", "default": 0}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "total_orders", "fieldtype": "Int", "label": "Total Orders", "default": 0, "read_only": 1}, {"fieldname": "total_spent", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Spent", "default": 0, "read_only": 1}, {"fieldname": "last_order_date", "fieldtype": "Date", "label": "Last Order Date", "read_only": 1}, {"fieldname": "preferences_section", "fieldtype": "Section Break", "label": "Preferences"}, {"fieldname": "preferred_paper_size", "fieldtype": "Link", "label": "Preferred Paper Size", "options": "Print Paper Size"}, {"fieldname": "preferred_paper_type", "fieldtype": "Link", "label": "Preferred Paper Type", "options": "Print Paper Type"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "preferred_pickup_location", "fieldtype": "Link", "label": "Preferred Pickup Location", "options": "Pickup Location"}, {"fieldname": "notification_preferences", "fieldtype": "Select", "label": "Notification Preferences", "options": "Email\nSMS\nBoth\nNone", "default": "Email"}], "modified": "2024-01-01 00:00:00", "module": "Pronto", "name": "Pronto Customer", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"read": 1, "role": "Customer", "if_owner": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}