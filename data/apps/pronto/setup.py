from setuptools import setup, find_packages

with open("requirements.txt") as f:
	install_requires = f.read().strip().split("\n")

# get version from __version__ variable in pronto/__init__.py
from pronto import __version__ as version

setup(
	name="pronto",
	version=version,
	description="Print Order Management System",
	author="Pronto Team",
	author_email="<EMAIL>",
	packages=find_packages(),
	zip_safe=False,
	include_package_data=True,
	install_requires=install_requires
)
