{"background_workers": 1, "db_host": "postgresql", "db_type": "postgres", "default_site": "development.localhost/", "developer_mode": 1, "file_watcher_port": 6787, "frappe_user": "frappe", "gunicorn_workers": 23, "live_reload": true, "prefer_server_url": true, "rebase_on_pull": false, "redis_cache": "redis://redis-cache:6379", "redis_queue": "redis://redis-queue:6380", "redis_socketio": "redis://redis-socketio:6381", "redis_db": "redis://redis-db:13311", "restart_supervisor_on_update": false, "restart_systemd_on_update": false, "serve_default_site": true, "shallow_clone": true, "socketio_port": 9000, "use_redis_auth": false, "use_x_forwarded_host": true, "webserver_port": 443}