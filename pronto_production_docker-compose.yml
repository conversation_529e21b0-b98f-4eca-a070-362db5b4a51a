version: '3.8'

services:
  postgresql:
    image: postgres:14
    container_name: pronto_postgresql
    environment:
      POSTGRES_DB: pronto_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 123
      POSTGRES_HOST_AUTH_METHOD: trust
    volumes:
      - ./data/postgresql:/var/lib/postgresql/data
    ports:
      - "5439:5432"
    networks:
      - pronto_network
    restart: unless-stopped

  redis-cache:
    image: redis:7-alpine
    container_name: pronto_redis_cache
    volumes:
      - ./data/redis-cache:/data
    ports:
      - "6380:6379"
    networks:
      - pronto_network
    restart: unless-stopped

  redis-queue:
    image: redis:7-alpine
    container_name: pronto_redis_queue
    volumes:
      - ./data/redis-queue:/data
    ports:
      - "6381:6379"
    networks:
      - pronto_network
    restart: unless-stopped

  frappe:
    image: frappe/erpnext:version-15
    container_name: pronto_frappe
    user: "1000:1000"
    environment:
      - DB_HOST=postgresql
      - DB_TYPE=postgres
      - REDIS_CACHE=redis://redis-cache:6379
      - REDIS_QUEUE=redis://redis-queue:6379
    volumes:
      - ./data/sites/pronto.ng:/home/<USER>/frappe-bench/sites/pronto.ng
      - ./data/apps/pronto:/home/<USER>/frappe-bench/apps/pronto
      - ./docker_startup.sh:/home/<USER>/startup.sh
    ports:
      - "8080:8000"
    networks:
      - pronto_network
    depends_on:
      - postgresql
      - redis-cache
      - redis-queue
    restart: unless-stopped
    entrypoint: ["/bin/bash", "/home/<USER>/startup.sh"]

networks:
  pronto_network:
    driver: bridge