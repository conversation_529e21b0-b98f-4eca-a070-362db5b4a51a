#!/bin/bash
set -e

echo "Starting Pronto Frappe Docker Environment..."

# Wait for database to be ready
echo "Waiting for PostgreSQL to be ready..."
until pg_isready -h postgresql -p 5432 -U postgres; do
  echo "PostgreSQL is unavailable - sleeping"
  sleep 2
done
echo "PostgreSQL is ready!"

# Wait for <PERSON><PERSON> to be ready (simplified check)
echo "Waiting for Redis to be ready..."
sleep 5
echo "Redis should be ready!"

# Change to frappe user and bench directory
cd /home/<USER>/frappe-bench

# Activate the bench environment
source env/bin/activate

# Create logs directory if it doesn't exist
mkdir -p sites/pronto.ng/logs

# Check if site exists, if not create it
if [ ! -f "sites/pronto.ng/site_config.json" ]; then
    echo "Creating new site pronto.ng..."
    bench new-site pronto.ng \
        --db-type postgres \
        --db-host postgresql \
        --db-port 5432 \
        --db-name pronto_db \
        --admin-password admin \
        --no-mariadb-socket
fi

# Check if pronto app exists, if not create it
if [ ! -d "apps/pronto" ]; then
    echo "Creating Pronto app..."
    bench new-app pronto --no-git
fi

# Install app on site if not already installed
if ! bench --site pronto.ng list-apps | grep -q "pronto"; then
    echo "Installing Pronto app on site..."
    bench --site pronto.ng install-app pronto
fi

# Set developer mode
bench --site pronto.ng set-config developer_mode 1

# Set default site
bench use pronto.ng

# Migrate database
echo "Running database migrations..."
bench --site pronto.ng migrate

# Build assets
echo "Building assets..."
bench build --app pronto

# Start the development server
echo "Starting Frappe development server..."
exec bench serve --port 8000 --host 0.0.0.0